import { Outlet } from 'react-router-dom';
import { motion } from 'framer-motion';

/**
 * Layout component for authentication pages
 * Provides a clean, focused layout for login and registration
 */
const AuthLayout = () => {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-background to-muted p-4">
      <motion.div 
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="w-full max-w-md"
      >
        <div className="bg-card shadow-lg rounded-xl p-8 border">
          <Outlet />
        </div>
      </motion.div>
    </div>
  );
};

export default AuthLayout;
