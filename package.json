{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@radix-ui/react-slot": "^1.2.2", "@supabase/supabase-js": "^2.49.4", "@tailwindcss/vite": "^4.1.5", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "firebase": "^11.7.1", "lucide-react": "^0.509.0", "motion": "^12.10.5", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router-dom": "^6.30.0", "tailwind-merge": "^3.2.0", "tailwindcss": "^4.1.5", "uuid": "^11.1.0"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/node": "^22.15.17", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "tw-animate-css": "^1.2.9", "vite": "^6.3.5"}}